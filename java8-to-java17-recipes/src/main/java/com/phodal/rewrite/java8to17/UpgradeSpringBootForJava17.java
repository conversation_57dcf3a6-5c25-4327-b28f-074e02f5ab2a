/*
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.phodal.rewrite.java8to17;

import lombok.EqualsAndHashCode;
import lombok.Value;
import org.openrewrite.*;
import org.openrewrite.maven.MavenIsoVisitor;
import org.openrewrite.maven.tree.ResolvedDependency;
import org.openrewrite.maven.tree.ResolvedManagedDependency;
import org.openrewrite.semver.Semver;
import org.openrewrite.xml.tree.Xml;

import java.util.Optional;

@Value
@EqualsAndHashCode(callSuper = false)
public class UpgradeSpringBootForJava17 extends Recipe {

    @Option(displayName = "Target Spring Boot version",
            description = "The Spring Boot version to upgrade to (must be compatible with Java 17).",
            example = "2.7.18")
    String targetVersion;

    @Override
    public String getDisplayName() {
        return "Upgrade Spring Boot for Java 17 compatibility";
    }

    @Override
    public String getDescription() {
        return "Upgrade Spring Boot version to " + targetVersion + " which is compatible with Java 17. " +
               "This recipe updates the Spring Boot parent version and related dependencies.";
    }

    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new MavenIsoVisitor<ExecutionContext>() {
            @Override
            public Xml.Tag visitTag(Xml.Tag tag, ExecutionContext ctx) {
                Xml.Tag t = super.visitTag(tag, ctx);
                
                // Update Spring Boot parent version
                if (isParentTag()) {
                    Optional<Xml.Tag> groupId = tag.getChild("groupId");
                    if (groupId.isPresent() && 
                        "org.springframework.boot".equals(groupId.get().getValue().orElse(""))) {
                        Optional<Xml.Tag> version = tag.getChild("version");
                        if (version.isPresent()) {
                            String currentVersion = version.get().getValue().orElse("");
                            if (shouldUpgradeVersion(currentVersion)) {
                                return t.withContent(t.getContent().stream()
                                    .map(content -> {
                                        if (content instanceof Xml.Tag) {
                                            Xml.Tag childTag = (Xml.Tag) content;
                                            if ("version".equals(childTag.getName())) {
                                                return childTag.withValue(targetVersion);
                                            }
                                        }
                                        return content;
                                    })
                                    .collect(java.util.stream.Collectors.toList()));
                            }
                        }
                    }
                }
                
                // Update Spring Boot dependencies
                if (isDependencyTag()) {
                    ResolvedDependency dependency = findDependency(tag);
                    if (dependency != null && 
                        "org.springframework.boot".equals(dependency.getGroupId()) &&
                        shouldUpgradeVersion(dependency.getVersion())) {
                        return updateDependencyVersion(t);
                    }
                }
                
                // Update Spring Boot managed dependencies
                if (isManagedDependencyTag()) {
                    ResolvedManagedDependency managedDependency = findManagedDependency(tag);
                    if (managedDependency != null && 
                        "org.springframework.boot".equals(managedDependency.getGroupId()) &&
                        shouldUpgradeVersion(managedDependency.getVersion())) {
                        return updateDependencyVersion(t);
                    }
                }
                
                return t;
            }
            
            private boolean shouldUpgradeVersion(String currentVersion) {
                if (currentVersion == null || currentVersion.isEmpty()) {
                    return false;
                }
                
                try {
                    // Check if current version is older than target version
                    return Semver.validate(currentVersion, null).isValid() &&
                           Semver.validate(targetVersion, null).isValid() &&
                           compareVersions(currentVersion, targetVersion) < 0;
                } catch (Exception e) {
                    // If version comparison fails, check for known old versions
                    return isOldSpringBootVersion(currentVersion);
                }
            }
            
            private boolean isOldSpringBootVersion(String version) {
                // Known old Spring Boot versions that need upgrading for Java 17
                return version.startsWith("1.") || 
                       version.startsWith("2.0.") ||
                       version.startsWith("2.1.") ||
                       version.startsWith("2.2.") ||
                       version.startsWith("2.3.") ||
                       version.startsWith("2.4.") ||
                       version.startsWith("2.5.") ||
                       (version.startsWith("2.6.") && compareVersions(version, "2.6.15") < 0) ||
                       (version.startsWith("2.7.") && compareVersions(version, "2.7.0") < 0);
            }
            
            private int compareVersions(String version1, String version2) {
                String[] parts1 = version1.split("\\.");
                String[] parts2 = version2.split("\\.");
                
                int maxLength = Math.max(parts1.length, parts2.length);
                for (int i = 0; i < maxLength; i++) {
                    int v1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
                    int v2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;
                    
                    if (v1 != v2) {
                        return Integer.compare(v1, v2);
                    }
                }
                return 0;
            }
            
            private int parseVersionPart(String part) {
                try {
                    // Extract numeric part, ignoring qualifiers like "RELEASE", "SNAPSHOT", etc.
                    String numericPart = part.replaceAll("[^0-9].*", "");
                    return numericPart.isEmpty() ? 0 : Integer.parseInt(numericPart);
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
            
            private Xml.Tag updateDependencyVersion(Xml.Tag dependencyTag) {
                return dependencyTag.withContent(dependencyTag.getContent().stream()
                    .map(content -> {
                        if (content instanceof Xml.Tag) {
                            Xml.Tag childTag = (Xml.Tag) content;
                            if ("version".equals(childTag.getName())) {
                                return childTag.withValue(targetVersion);
                            }
                        }
                        return content;
                    })
                    .collect(java.util.stream.Collectors.toList()));
            }
        };
    }
}
