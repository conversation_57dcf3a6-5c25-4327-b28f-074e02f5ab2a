<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" version="3.0.2" name="com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17Test" time="0.038" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/test-classes:/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/classes:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java/8.37.1/rewrite-java-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-core/8.37.1/rewrite-core-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/tools/jgit/1.2.0/jgit-1.2.0.jar:/Users/<USER>/.m2/repository/com/googlecode/javaewah/JavaEWAH/1.1.13/JavaEWAH-1.1.13.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.17.2/jackson-core-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.17.2/jackson-databind-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.17.2/jackson-module-parameter-names-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.17.2/jackson-datatype-jsr310-2.17.2.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/tools/java-object-diff/1.0.1/java-object-diff-1.0.1.jar:/Users/<USER>/.m2/repository/io/quarkus/gizmo/gizmo/1.0.11.Final/gizmo-1.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna-platform/5.15.0/jna-platform-5.15.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.15.0/jna-5.15.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-yaml/8.37.1/rewrite-yaml-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-xml/8.37.1/rewrite-xml-8.37.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.9.17/micrometer-core-1.9.17.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/26.0.0/annotations-26.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.17.2/jackson-annotations-2.17.2.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.11.1/antlr4-runtime-4.11.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.12.0/commons-text-1.12.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.177/classgraph-4.8.177.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.7/snappy-java-1.1.10.7.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-8/8.37.1/rewrite-java-8-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-11/8.37.1/rewrite-java-11-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-17/8.37.1/rewrite-java-17-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-maven/8.37.1/rewrite-maven-8.37.1.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.9.3/caffeine-2.9.3.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.19.0/checker-qual-3.19.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.10.0/error_prone_annotations-2.10.0.jar:/Users/<USER>/.m2/repository/dev/failsafe/failsafe/3.3.2/failsafe-3.3.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.17.2/jackson-dataformat-xml-2.17.2.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.2/stax2-api-4.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.7.0/woodstox-core-6.7.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.17.2/jackson-dataformat-smile-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.17.2/jackson-module-jaxb-annotations-2.17.2.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.17.2/jackson-datatype-jdk8-2.17.2.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-properties/8.37.1/rewrite-properties-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-gradle/8.37.1/rewrite-gradle-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-groovy/8.37.1/rewrite-groovy-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-spring/5.21.0/rewrite-spring-5.21.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-templating/1.16.1/rewrite-templating-1.16.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-java-dependencies/1.20.0/rewrite-java-dependencies-1.20.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-static-analysis/1.18.0/rewrite-static-analysis-1.18.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-kotlin/1.21.0/rewrite-kotlin-1.21.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.9.22/kotlin-compiler-embeddable-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-script-runtime/1.9.22/kotlin-script-runtime-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-reflect/1.6.10/kotlin-reflect-1.6.10.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.9.22/kotlin-daemon-embeddable-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.9.22/kotlin-stdlib-1.9.22.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-csharp/0.14.1/rewrite-csharp-0.14.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.17.2/jackson-dataformat-cbor-2.17.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.13/logback-classic-1.2.13.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.13/logback-core-1.2.13.jar:/Users/<USER>/.m2/repository/org/openrewrite/meta/rewrite-analysis/2.11.1/rewrite-analysis-2.11.1.jar:/Users/<USER>/.m2/repository/org/functionaljava/functionaljava/5.0/functionaljava-5.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/gradle/tooling/model/2.6.4/model-2.6.4.jar:/Users/<USER>/.m2/repository/org/mongodb/mongo-java-driver/3.12.14/mongo-java-driver-3.12.14.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/2.2.12.RELEASE/spring-data-mongodb-2.2.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.12.RELEASE/spring-data-commons-2.2.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-apache/1.8.0/rewrite-apache-1.8.0.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.16/poi-3.16.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar:/Users/<USER>/.m2/repository/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-hibernate/1.10.0/rewrite-hibernate-1.10.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-micrometer/0.9.0/rewrite-micrometer-0.9.0.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.23/metrics-core-4.2.23.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-migrate-java/2.26.1/rewrite-migrate-java-2.26.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-github-actions/2.8.1/rewrite-github-actions-2.8.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-jenkins/0.14.1/rewrite-jenkins-0.14.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-21/8.37.1/rewrite-java-21-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-openapi/0.8.0/rewrite-openapi-0.8.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-reactive-streams/0.3.0/rewrite-reactive-streams-0.3.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.10/reactor-core-3.6.10.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-testing-frameworks/2.20.0/rewrite-testing-frameworks-2.20.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.2/testcontainers-1.20.2.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.0/docker-java-api-3.4.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.0/docker-java-transport-zerodep-3.4.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.0/docker-java-transport-3.4.0.jar:/Users/<USER>/.m2/repository/tech/picnic/error-prone-support/error-prone-contrib/0.18.0/error-prone-contrib-0.18.0-recipes.jar:/Users/<USER>/.m2/repository/tech/picnic/error-prone-support/error-prone-utils/0.18.0/error-prone-utils-0.18.0.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-test/8.37.1/rewrite-test-8.37.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.17.2/jackson-dataformat-csv-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.0/junit-jupiter-api-5.11.0.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.0/junit-platform-commons-1.11.0.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.0/junit-jupiter-params-5.11.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.0/junit-jupiter-engine-5.11.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.0/junit-platform-engine-1.11.0.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.18/byte-buddy-1.14.18.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/surefire/surefirebooter-20250721231021611_3.jar /Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/surefire 2025-07-21T23-10-15_522-jvmRun1 surefire-20250721231021611_1tmp surefire_0-20250721231021611_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/test-classes:/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/classes:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java/8.37.1/rewrite-java-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-core/8.37.1/rewrite-core-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/tools/jgit/1.2.0/jgit-1.2.0.jar:/Users/<USER>/.m2/repository/com/googlecode/javaewah/JavaEWAH/1.1.13/JavaEWAH-1.1.13.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.17.2/jackson-core-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.17.2/jackson-databind-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.17.2/jackson-module-parameter-names-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.17.2/jackson-datatype-jsr310-2.17.2.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/tools/java-object-diff/1.0.1/java-object-diff-1.0.1.jar:/Users/<USER>/.m2/repository/io/quarkus/gizmo/gizmo/1.0.11.Final/gizmo-1.0.11.Final.jar:/Users/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna-platform/5.15.0/jna-platform-5.15.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.15.0/jna-5.15.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-yaml/8.37.1/rewrite-yaml-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-xml/8.37.1/rewrite-xml-8.37.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.9.17/micrometer-core-1.9.17.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/26.0.0/annotations-26.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.17.2/jackson-annotations-2.17.2.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.11.1/antlr4-runtime-4.11.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.12.0/commons-text-1.12.0.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.177/classgraph-4.8.177.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.7/snappy-java-1.1.10.7.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-8/8.37.1/rewrite-java-8-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-11/8.37.1/rewrite-java-11-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-17/8.37.1/rewrite-java-17-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-maven/8.37.1/rewrite-maven-8.37.1.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.9.3/caffeine-2.9.3.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.19.0/checker-qual-3.19.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.10.0/error_prone_annotations-2.10.0.jar:/Users/<USER>/.m2/repository/dev/failsafe/failsafe/3.3.2/failsafe-3.3.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.17.2/jackson-dataformat-xml-2.17.2.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.2/stax2-api-4.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.7.0/woodstox-core-6.7.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.17.2/jackson-dataformat-smile-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.17.2/jackson-module-jaxb-annotations-2.17.2.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.17.2/jackson-datatype-jdk8-2.17.2.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-properties/8.37.1/rewrite-properties-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-gradle/8.37.1/rewrite-gradle-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-groovy/8.37.1/rewrite-groovy-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-spring/5.21.0/rewrite-spring-5.21.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-templating/1.16.1/rewrite-templating-1.16.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-java-dependencies/1.20.0/rewrite-java-dependencies-1.20.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-static-analysis/1.18.0/rewrite-static-analysis-1.18.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-kotlin/1.21.0/rewrite-kotlin-1.21.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.9.22/kotlin-compiler-embeddable-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-script-runtime/1.9.22/kotlin-script-runtime-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-reflect/1.6.10/kotlin-reflect-1.6.10.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.9.22/kotlin-daemon-embeddable-1.9.22.jar:/Users/<USER>/.m2/repository/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.9.22/kotlin-stdlib-1.9.22.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-csharp/0.14.1/rewrite-csharp-0.14.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.17.2/jackson-dataformat-cbor-2.17.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.13/logback-classic-1.2.13.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.13/logback-core-1.2.13.jar:/Users/<USER>/.m2/repository/org/openrewrite/meta/rewrite-analysis/2.11.1/rewrite-analysis-2.11.1.jar:/Users/<USER>/.m2/repository/org/functionaljava/functionaljava/5.0/functionaljava-5.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/gradle/tooling/model/2.6.4/model-2.6.4.jar:/Users/<USER>/.m2/repository/org/mongodb/mongo-java-driver/3.12.14/mongo-java-driver-3.12.14.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/2.2.12.RELEASE/spring-data-mongodb-2.2.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.12.RELEASE/spring-data-commons-2.2.12.RELEASE.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-apache/1.8.0/rewrite-apache-1.8.0.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.17.0/commons-io-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/maven/shared/maven-shared-utils/3.4.2/maven-shared-utils-3.4.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.16/poi-3.16.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar:/Users/<USER>/.m2/repository/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-hibernate/1.10.0/rewrite-hibernate-1.10.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-micrometer/0.9.0/rewrite-micrometer-0.9.0.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.23/metrics-core-4.2.23.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-migrate-java/2.26.1/rewrite-migrate-java-2.26.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-github-actions/2.8.1/rewrite-github-actions-2.8.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-jenkins/0.14.1/rewrite-jenkins-0.14.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-java-21/8.37.1/rewrite-java-21-8.37.1.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-openapi/0.8.0/rewrite-openapi-0.8.0.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-reactive-streams/0.3.0/rewrite-reactive-streams-0.3.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.10/reactor-core-3.6.10.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/openrewrite/recipe/rewrite-testing-frameworks/2.20.0/rewrite-testing-frameworks-2.20.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.20.2/testcontainers-1.20.2.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.4.0/docker-java-api-3.4.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.4.0/docker-java-transport-zerodep-3.4.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.4.0/docker-java-transport-3.4.0.jar:/Users/<USER>/.m2/repository/tech/picnic/error-prone-support/error-prone-contrib/0.18.0/error-prone-contrib-0.18.0-recipes.jar:/Users/<USER>/.m2/repository/tech/picnic/error-prone-support/error-prone-utils/0.18.0/error-prone-utils-0.18.0.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/Users/<USER>/.m2/repository/org/openrewrite/rewrite-test/8.37.1/rewrite-test-8.37.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.17.2/jackson-dataformat-csv-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.11.0/junit-jupiter-api-5.11.0.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.11.0/junit-platform-commons-1.11.0.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.11.0/junit-jupiter-params-5.11.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.11.0/junit-jupiter-engine-5.11.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.11.0/junit-platform-engine-1.11.0.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.26.3/assertj-core-3.26.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.18/byte-buddy-1.14.18.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes/target/surefire/surefirebooter-20250721231021611_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="phodal"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/rt/gw2rs2td209ck8nlqdlt_v8m0000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/test/java8-java17/java8-to-java17-recipes"/>
    <property name="os.arch" value="x86_64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="upgradeSpringBootParentVersion" classname="com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17Test" time="0.016"/>
  <testcase name="upgradeSpringBootDependencyVersion" classname="com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17Test" time="0.001"/>
  <testcase name="shouldNotDowngradeNewerVersion" classname="com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17Test" time="0.001"/>
  <testcase name="upgradeOldSpringBoot2xVersions" classname="com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17Test" time="0.001"/>
</testsuite>