-------------------------------------------------------------------------------
Test set: com.phodal.rewrite.java8to17.UpgradeMavenJavaVersionTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.992 s <<< FAILURE! -- in com.phodal.rewrite.java8to17.UpgradeMavenJavaVersionTest
com.phodal.rewrite.java8to17.UpgradeMavenJavaVersionTest.upgradeJavaVersionInCompilerPlugin -- Time elapsed: 0.824 s <<< FAILURE!
java.lang.AssertionError: Expected recipe to complete in 1 cycle, but took 2 cycles. This usually indicates the recipe is making changes after it should have stabilized.
	at org.openrewrite.test.LargeSourceSetCheckingExpectedCycles.afterCycle(LargeSourceSetCheckingExpectedCycles.java:121)
	at org.openrewrite.RecipeScheduler.runRecipeCycles(RecipeScheduler.java:98)
	at org.openrewrite.RecipeScheduler.scheduleRun(RecipeScheduler.java:41)
	at org.openrewrite.Recipe.run(Recipe.java:376)
	at org.openrewrite.test.RewriteTest.rewriteRun(RewriteTest.java:373)
	at org.openrewrite.test.RewriteTest.rewriteRun(RewriteTest.java:132)
	at org.openrewrite.test.RewriteTest.rewriteRun(RewriteTest.java:127)
	at com.phodal.rewrite.java8to17.UpgradeMavenJavaVersionTest.upgradeJavaVersionInCompilerPlugin(UpgradeMavenJavaVersionTest.java:71)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)

