#
# Copyright 2024 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.Java8ToJava17Migration
displayName: Migrate from Java 8 to Java 17
description: Comprehensive migration from Java 8 to Java 17 including build configuration, dependency updates, and compatibility fixes.
tags:
  - java
  - migration
  - java17
  - spring-boot
recipeList:
  - com.phodal.rewrite.java8to17.UpgradeMavenJavaVersion:
      javaVersion: "17"
  - com.phodal.rewrite.java8to17.UpgradeGradleJavaVersion:
      javaVersion: "17"
  - com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17:
      targetVersion: "2.7.18"
  - org.openrewrite.java.dependencies.UpgradeDependencyVersion:
      groupId: org.apache.tomcat.embed
      artifactId: tomcat-embed-*
      newVersion: 9.0.x
  - com.phodal.rewrite.java8to17.FixJava17Compatibility
  - com.phodal.rewrite.java8to17.FixSpringBootJava17Compatibility
  - org.openrewrite.java.dependencies.UpgradeDependencyVersion:
      groupId: ch.qos.logback
      artifactId: logback-*
      newVersion: 1.2.x
  - com.phodal.rewrite.java8to17.RemoveDeprecatedDependencies

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.Java8ToJava17MigrationWithSpringBoot3
displayName: Migrate from Java 8 to Java 17 with Spring Boot 3
description: Comprehensive migration from Java 8 to Java 17 with Spring Boot 3 upgrade.
tags:
  - java
  - migration
  - java17
  - spring-boot
  - spring-boot-3
recipeList:
  - com.phodal.rewrite.java8to17.UpgradeMavenJavaVersion:
      javaVersion: "17"
  - com.phodal.rewrite.java8to17.UpgradeGradleJavaVersion:
      javaVersion: "17"
  - com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17:
      targetVersion: "3.2.0"
  - org.openrewrite.java.dependencies.UpgradeDependencyVersion:
      groupId: org.apache.tomcat.embed
      artifactId: tomcat-embed-*
      newVersion: 10.1.x
  - com.phodal.rewrite.java8to17.FixJava17Compatibility
  - com.phodal.rewrite.java8to17.FixSpringBootJava17Compatibility
  - org.openrewrite.java.dependencies.UpgradeDependencyVersion:
      groupId: ch.qos.logback
      artifactId: logback-*
      newVersion: 1.4.x
  - com.phodal.rewrite.java8to17.RemoveDeprecatedDependencies

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.UpgradeMavenJavaVersion
displayName: Upgrade Maven Java version
description: Upgrade Maven Java compiler version from 1.8 to specified version.
options:
  - name: javaVersion
    displayName: Java version
    description: The Java version to upgrade to.
    type: String
    required: true
    example: "17"

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.UpgradeGradleJavaVersion
displayName: Upgrade Gradle Java version
description: Upgrade Gradle Java compiler version from 8 to specified version.
options:
  - name: javaVersion
    displayName: Java version
    description: The Java version to upgrade to.
    type: String
    required: true
    example: "17"

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.UpgradeSpringBootForJava17
displayName: Upgrade Spring Boot for Java 17 compatibility
description: Upgrade Spring Boot version to one that is compatible with Java 17.
options:
  - name: targetVersion
    displayName: Target Spring Boot version
    description: The Spring Boot version to upgrade to (must be compatible with Java 17).
    type: String
    required: true
    example: "2.7.18"

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.FixJava17Compatibility
displayName: Fix Java 17 compatibility issues
description: Fix common Java 17 compatibility issues including deprecated APIs and removed methods.

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.FixSpringBootJava17Compatibility
displayName: Fix Spring Boot Java 17 compatibility issues
description: Fix Spring Boot specific compatibility issues when upgrading to Java 17.

---
type: specs.openrewrite.org/v1beta/recipe
name: com.phodal.rewrite.java8to17.RemoveDeprecatedDependencies
displayName: Remove deprecated dependencies
description: Remove dependencies that are deprecated or incompatible with Java 17.
